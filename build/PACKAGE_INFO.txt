TeamSphere Linux Installer Package
==================================

Package: teamsphere-installer-1.0.0.tar.gz
Version: 1.0.0
Build Date: Wednesday 23 July 2025 11:24:35 AM IST
Archive Size: 83M

Contents:
- TeamSphere application source code
- Pre-compiled JAR and class files
- Installation scripts
- Management tools
- Documentation

Installation:
1. Extract: tar -xzf teamsphere-installer-1.0.0.tar.gz
2. Enter directory: cd teamsphere-installer-1.0.0
3. Run installer: ./teamsphere-installer.sh

System Requirements:
- Linux (Ubuntu 18.04+, Debian 10+, CentOS 7+, RHEL 7+, Fedora 30+)
- 2GB RAM minimum (4GB recommended)
- 5GB disk space
- Internet connection for dependencies

Support:
- See README.md for detailed instructions
- See QUICK_START.md for quick installation
- Use teamsphere-manager.sh for application management

SHA256 Checksum:
0b10054dd4bd72c64db22e41b0c38cf0aa9d483ff1cbe18ce6238fbd24dcb74a
